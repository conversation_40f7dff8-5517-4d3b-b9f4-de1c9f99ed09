spring:
  main:
    allow-circular-references: true
    allow-bean-definition-overriding: true
  profiles:
    active:
      - gs-engine
      - gs-common
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
    async:
      request-timeout: 300000 #5min
  messages:
    basename: i18n/message
    cache-duration: 3600
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.zaxxer.hikari.HikariDataSource
    url: jdbc:mysql://${app.db.kms.host}:${app.db.kms.port}/${app.db.kms.dbname}?useUnicode=true&characterEncoding=utf8&useSSL=false&allowMultiQueries=true
    username: ${app.db.kms.username}
    password: ${app.db.kms.password}
    hikari:
      connection-timeout: 2000
      minimum-idle: 5
      maximum-pool-size: 15
      auto-commit: true
#  redis:
#    database: 0
#    connect-timeout: 10s
#    pool:
#      maxactive: 64
#      maxidle: 64
#      maxwait: -1
#      minidle: 1
#    scan-batch-size: 10000
  aop:
    proxy-target-class: true
  servlet:
    multipart:
      max-file-size: 200MB
      max-request-size: 10GB
  kafka:
    bootstrap-servers: ${app.kafka.servers:127.0.0.1:9092} # kafka集群信息，多个节点通过“,”分隔
    producer:
      retries: 3
      batch-size: 16384 # 16K
      buffer-memory: 33554432 # 32M 缓冲区大小
      acks: 1 # 应答级别:多少个分区副本备份完成时向生产者发送ack确认(可选0、1、all/-1)
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
    consumer:
      group-id: telecom-ai-kms # 消费者组
      enable-auto-commit: true # 自动提交
      auto-offset-reset: latest
      # 当kafka中没有初始offset或offset超出范围时将自动重置offset
      # earliest:重置为分区中最小的offset;
      # latest:重置为分区中最新的offset(消费分区中新产生的数据);
      # none:只要有一个分区不存在已提交的offset,就抛出异常;
      max-poll-records: 50 # 批量消费每次最多消费多少条消息
      # 指定消息key和消息体的编解码方式
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
    listener:
      poll-timeout: 1500
      missing-topics-fatal: false  # 消费端监听的topic不存在时，项目启动会报错(关掉)
      #type: batch # 批量消费
  elasticsearch:
    uris: ${app.es.address}
    username: ${app.es.username}
    password: ${app.es.password}
    connection-timeout: ${app.es.connection-timeout:5}
server:
  port: 8092
  servlet:
    context-path: /ais
    encoding:
      charset: UTF-8

mybatis-plus:
  mapperLocations: classpath*:/mybatis/**/*.xml
  configuration:
    mapUnderscoreToCamelCase: true
    cacheEnabled: true #默认开启二级缓存
    localCacheScope: STATEMENT # 关闭一级缓存
  global-config:
    banner: false
    db-config:
      logic-delete-field: yn # 全局逻辑删除的实体字段名
      logic-delete-value: id
      logic-not-delete-value: 0


management:
  health:
    elasticsearch:
      enabled: false

telecom:
  ai:
    search:
      client:
        socketTimeout: 120s

liteflow:
  rule-source: flow/kms_flow.el.xml
  printBanner: false
  checkNodeExists: true
  printExecutionLog: false
  thread-executor-class: com.chinatelecom.gs.engine.kms.common.LiteDefaultExecutorBuilder

agentinit:
  agentName: "小智"
  agentPicture: "/ais/bot/web/defaultIcon"
  agentDesc: "全能机器人，为您提供全方位的服务支持。"
  agentReply: "你是一个基于大语言模型的AI助手。现在给你一个用户的提问，请给出简洁、清晰且准确的回答。你将会收到一系列与问题相关的上下文，每个上下文都以\"```[x]```\"这样的特殊的参考标记开头，其中x为上下文编号（从0开始计数），每个上下文由文章标题以及关联文本片段构成。在回答问题时，请使用上下文。同时你的回答必须正确、准确，并由专家以中立、专业的语气书写。请限制在{max_token}个token以内。不要提供与问题无关的任何信息，也不要重复。除了代码、具体名称和引用外，你的回答必须用中文书写。"
  modelCode: "model_992668744299715570"
  modelName: "DeepSeek-R1-Qwen32b"
  multiModelCode: "model_992668744299712569"
  multiModelName: "多模态模型"
  modelMode: "PRECISION_MODE"
  modelRandomness: 0.1
  memoryCount: 1
  outputType: "txt"
  enableAdvConf: true
  enableSortModel: true
  agentPrologue: "您好！我是您的智能伙伴Hi 小智。致力于为您提供精准、高效的知识解答。快来向我提问吧，让我用智能为您开启知识探索之旅！"
  agentGuide: ["如何用AI提高工作效率？","你能帮我做什么呢？"]
  noAnswerScript: "对不起，我无法回答你的问题。你是否有其他问题需要帮助？"
  agentSuggest: true
  suggestCount: 3
  globalVarSwitch: false
  securityFenceSwitch: false
  securityFenceScript: "检测到您输入的内容可能不太合适哦~请换一种表达方式吧!感谢您的理解!"
  noAnswerCount: 3
  noAnswerCountSwitch: true
  noAnswerCountType: CONTINUE_TRIGGER
  manuCount: 3
  manuCountSwitch: true
  manuCountType: CONTINUE_TRIGGER
  manuKeyWordList: ["转人工"]
  manuScript: "您好，为了更好地帮助您，我将为您转接人工客服，请稍等片刻，感谢您的理解!"
  chatLLMSwitch: false
  isDefault: "1"
  manuInstructCode: "system_to_manual"

deepseekinit:
  agentName: "DeepSeek"
  agentPicture: "/ais/bot/web/defaultIcon?code=ds"
  agentDesc: "deepseek机器人，专注提供精准、深度的知识服务。擅长深度思考。技术解析、生活建议、学习指导，支持多轮对话与复杂问题拆解，以逻辑清晰、响应高效为核心优势。"
  agentReply: "你是一个充满智慧的AI伙伴，兼具专业性和亲和力。以高效、准确的回答著称，同时具备幽默感和创造力，能够与用户进行轻松愉快的对话。擅长逻辑推理和深度思考，致力于帮助用户解决问题，激发灵感。回答问题优先匹配预设知识库与实时数据，复杂问题自动拆解为子问题分步解答，引导用户深入思考，提供多角度的解决方案。多轮对话中主动追问关键细节。"
  modelCode: "model_992668744299715570"
  modelName: "DeepSeek-R1-Qwen32b"
  multiModelCode: "model_992668744299712569"
  multiModelName: "多模态模型"
  modelMode: "PRECISION_MODE"
  modelRandomness: 0.1
  memoryCount: 1
  outputType: "txt"
  enableAdvConf: true
  enableSortModel: true
  agentPrologue: "我是 DeepSeek，很高兴见到你！我可以帮你写代码、读文件、写作各种创意内容，请把你的任务交给我吧~"
  agentGuide: ["如何用AI提高工作效率？","你能帮我做什么呢？"]
  noAnswerScript: "我还在学习中，这个问题我暂时回答不了呢。不过你可以试试问一些别的问题哦。"
  agentSuggest: true
  suggestCount: 3
  globalVarSwitch: false
  securityFenceSwitch: false
  securityFenceScript: "检测到您输入的内容可能不太合适哦~请换一种表达方式吧!感谢您的理解!"
  noAnswerCount: 3
  noAnswerCountSwitch: true
  noAnswerCountType: CONTINUE_TRIGGER
  manuCount: 3
  manuCountSwitch: true
  manuCountType: CONTINUE_TRIGGER
  manuKeyWordList: [ "转人工" ]
  manuScript: "您好，为了更好地帮助您，我将为您转接人工客服，请稍等片刻，感谢您的理解!"
  chatLLMSwitch: true
  isDefault: "1"
  manuInstructCode: "system_to_manual"

gs:
  system:
    sourceUrlType:
      GS_ENGINE: "/ais/common/f"
      KS: "/ais/ks/f"
      BOT: "/ais/bot/f"
      OUTBOUND: "/ais/outbound/f"
      ASSIST: "/ais/assist/f"
      AIQC: "/ais/aiqc/f"
      AISP: "/ais/aisp/f"
      IM: "/ais/im/f"
      TELEPHONE: "/ais/telephone/f"
      GOVERNMENT: "/ais/government/f"
  search:
    searchTemplate:
      doc: "{\"query\":{\"bool\":{\"must\":[{\"bool\":{\"should\":[{\"match\":{\"fileContent\":{\"query\":\"${query?json_string}\"}}},{\"match\":{\"title\":{\"query\":\"${query?json_string}\"}}}]}}]}},\"knn\":{\"field\":\"fileContentVector\",\"query\":\"${query?json_string}\",\"k\":100,\"num_candidates\":1000},\"page\":1,\"size\":50,\"knnMinScore\":0.65,\"firstRankExpression\":\"atan_normalized_bm25(0.01)*0.5+knn_score()*0.5\",\"type\":\"VECTOR\"}"
      faq: "{\"query\":{\"bool\":{\"must\":[{\"bool\":{\"should\":[{\"match\":{\"fileContent\":{\"query\":\"${query?json_string}\"}}},{\"match\":{\"title\":{\"query\":\"${query?json_string}\"}}}]}}]}},\"knn\":{\"field\":\"fileContentVector\",\"query\":\"${query?json_string}\",\"k\":100,\"num_candidates\":1000},\"page\":1,\"size\":50,\"knnMinScore\":0.65,\"firstRankExpression\":\"atan_normalized_bm25(0.01)*0.5+knn_score()*0.5\",\"type\":\"VECTOR\"}"
      filter: "{\"distinct\":{\"distField\":\"knowledgeCode\",\"distTimes\":1,\"distCount\":1,\"collectFields\":[\"knowledgeCode\",\"file\",\"fileOriginalContent\",\"fileContent\",\"fileSource\",\"type\"],\"collectCount\":3},\"type\":\"TRADITION\",\"page\":1,\"size\":50}"
      search_doc: "{\"query\":{\"bool\":{\"must\":[{\"multi_match\":{\"query\":\"${query?json_string}\",\"fields\":[\"title\",\"fileContent\"],\"type\":\"most_fields\",\"tie_breaker\":0.3}}],\"should\":[{\"match_phrase\":{\"title\":{\"query\":\"${query?json_string}\",\"slop\":2,\"boost\":10}}},{\"match_phrase\":{\"fileContent\":{\"query\":\"${query?json_string}\",\"slop\":2,\"boost\":7}}},{\"match\":{\"title\":{\"query\":\"${query?json_string}\",\"boost\":4,\"minimum_should_match\":\"80%\"}}},{\"match\":{\"fileContent\":{\"query\":\"${query?json_string}\",\"boost\":3,\"minimum_should_match\":\"80%\"}}}]}},\"knn\":{\"field\":\"fileContentVector\",\"query\":\"${query?json_string}\",\"k\":100,\"num_candidates\":1000},\"page\":1,\"size\":50,\"knnMinScore\":0.65,\"firstRankExpression\":\"atan_normalized_bm25(0.01)*0.6+knn_score()*0.4\",\"type\":\"VECTOR\"}"
      search_faq: "{\"query\":{\"bool\":{\"must\":[{\"multi_match\":{\"query\":\"${query?json_string}\",\"fields\":[\"title\",\"fileContent\"],\"type\":\"most_fields\",\"tie_breaker\":0.3}}],\"should\":[{\"match_phrase\":{\"title\":{\"query\":\"${query?json_string}\",\"slop\":2,\"boost\":10}}},{\"match_phrase\":{\"fileContent\":{\"query\":\"${query?json_string}\",\"slop\":2,\"boost\":7}}},{\"match\":{\"title\":{\"query\":\"${query?json_string}\",\"boost\":4,\"minimum_should_match\":\"80%\"}}},{\"match\":{\"fileContent\":{\"query\":\"${query?json_string}\",\"boost\":3,\"minimum_should_match\":\"80%\"}}}]}},\"knn\":{\"field\":\"fileContentVector\",\"query\":\"${query?json_string}\",\"k\":100,\"num_candidates\":1000},\"page\":1,\"size\":50,\"knnMinScore\":0.65,\"firstRankExpression\":\"atan_normalized_bm25(0.01)*0.6+knn_score()*0.4\",\"type\":\"VECTOR\"}"
      search_filter: "{\"distinct\":{\"distField\":\"knowledgeCode\",\"distTimes\":1,\"distCount\":1,\"collectFields\":[\"knowledgeCode\",\"file\",\"fileOriginalContent\",\"fileContent\",\"fileSource\",\"type\"],\"collectCount\":3},\"type\":\"TRADITION\",\"page\":1,\"size\":50}"

# redis缓存会话记录时间，默认60分钟
dialog:
  message:
    maxMessageTime: 60


strategy:
  executorConfigs:
    - botType: 1
      actType: react
      className: com.chinatelecom.gs.engine.robot.dialog.execute.service.strategy.act.impl.OnlineStrategyExecutor
    - botType: 2
      actType: react
      className: com.chinatelecom.gs.engine.robot.dialog.execute.service.strategy.act.impl.IVRStrategyExecutor
    - botType: 3
      actType: react
      className: com.chinatelecom.gs.engine.robot.dialog.execute.service.strategy.act.impl.OutboundStrategyExecutor
    - botType: 1
      actType: flow
      className: com.chinatelecom.gs.engine.robot.dialog.execute.service.strategy.act.impl.OnlineStrategyExecutor
    - botType: 2
      actType: flow
      className: com.chinatelecom.gs.engine.robot.dialog.execute.service.strategy.act.impl.IVRStrategyExecutor
    - botType: 3
      actType: flow
      className: com.chinatelecom.gs.engine.robot.dialog.execute.service.strategy.act.impl.OutboundStrategyExecutor
