package com.chinatelecom.gs.engine.kms.flow.search.component;

import com.chinatelecom.gs.engine.common.utils.BaseFileUrlUtils;
import com.chinatelecom.gs.engine.kms.dto.KnowledgeBaseDTO;
import com.chinatelecom.gs.engine.kms.flow.BaseNodeComponent;
import com.chinatelecom.gs.engine.kms.flow.search.SearchContext;
import com.chinatelecom.gs.engine.kms.model.dto.KnowledgeDTO;
import com.chinatelecom.gs.engine.kms.repository.KnowledgeRepository;
import com.chinatelecom.gs.engine.kms.sdk.enums.KnowledgeBaseType;
import com.chinatelecom.gs.engine.kms.sdk.enums.KnowledgeType;
import com.chinatelecom.gs.engine.kms.sdk.enums.SceneType;
import com.chinatelecom.gs.engine.kms.sdk.vo.search.BaseItem;
import com.chinatelecom.gs.engine.kms.sdk.vo.search.SearchParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.search.SearchResp;
import com.chinatelecom.gs.engine.kms.sdk.vo.search.SearchVO;
import com.chinatelecom.gs.engine.kms.sdk.vo.search.sdk.SdkSearchResponse;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: gaoxianjun
 * @CreateTime: 2024-12-25
 * @Description:
 * @Version: 1.0
 */
@Slf4j
@LiteflowComponent(id = "SearchPage", name = "分页处理")
public class SearchPage extends BaseNodeComponent {

    @Resource
    private KnowledgeRepository knowledgeRepository;


    @Override
    public void process() throws Exception {
        SearchParam searchParam = this.getRequestData();
        SearchContext searchContext = this.getContextBean(SearchContext.class);
        Map<String, KnowledgeBaseDTO> searchKnowledgeBaseMap = searchContext.getKnowledgeBaseDTOMap();

        List<SdkSearchResponse.SdkRetrieveItem<BaseItem>> kbMatchItems = getKbMatchItems(searchParam, searchKnowledgeBaseMap);
        Integer pageNum = searchParam.getPageNum();
        Integer pageSize = searchParam.getPageSize();
        SearchResp<BaseItem> searchResp = searchContext.getSearchResp();
        if (searchResp == null || searchResp.getData() == null || CollectionUtils.isEmpty(searchResp.getData().getItems())) {
            // 如果只有知识库匹配结果，也需要返回
            if (!kbMatchItems.isEmpty()) {
                searchResp = new SearchResp<>();
                SearchVO<BaseItem> searchVO = new SearchVO<>();
                searchVO.setItems(kbMatchItems);
                searchVO.setTotal(kbMatchItems.size());
                searchResp.setData(searchVO);
                searchContext.setSearchResp(searchResp);
            }
            return;
        }

        // 获取所有搜索结果
        List<SdkSearchResponse.SdkRetrieveItem<BaseItem>> allItems = searchResp.getData().getItems();

        // 将搜索结果分为普通知识库结果和外部知识库结果
        List<SdkSearchResponse.SdkRetrieveItem<BaseItem>> normalItems = new ArrayList<>();
        List<SdkSearchResponse.SdkRetrieveItem<BaseItem>> externalItems = new ArrayList<>();

        for (SdkSearchResponse.SdkRetrieveItem<BaseItem> item : allItems) {
            if (isExternalKnowledgeBaseItem(item)) {
                externalItems.add(item);
            } else {
                normalItems.add(item);
            }
        }

        // 将知识库匹配结果和普通搜索结果合并，知识库匹配结果在前
        List<SdkSearchResponse.SdkRetrieveItem<BaseItem>> combinedItems = new ArrayList<>();
        combinedItems.addAll(kbMatchItems);
        combinedItems.addAll(normalItems);

        // 对合并后的结果进行分页处理
        List<SdkSearchResponse.SdkRetrieveItem<BaseItem>> pageItems = new ArrayList<>();
        int combinedItemSize = combinedItems.size();
        int fromIndex = (pageNum - 1) * pageSize;
        int toSize = pageNum * pageSize;

        if (combinedItemSize > fromIndex) {
            if (combinedItemSize < toSize) {
                toSize = combinedItemSize;
            }
            pageItems.addAll(combinedItems.subList(fromIndex, toSize));
        }

        // 获取知识信息（只对非知识库匹配项和非外部知识库项）
        if (!pageItems.isEmpty()) {
            fetchKnowledgeInfo(pageItems);
        }

        // 将外部知识库结果追加到分页后的结果列表中
        if (!externalItems.isEmpty()) {
            log.info("追加外部知识库搜索结果到分页结果中，外部结果数量: {}", externalItems.size());
            pageItems.addAll(externalItems);
        }

        // 更新搜索结果
        searchResp.getData().setItems(pageItems);

        // 更新总数（知识库匹配数量 + 普通知识库的总数 + 外部知识库的数量）
        int totalCount = kbMatchItems.size() + normalItems.size() + externalItems.size();
        searchResp.getData().setTotal(totalCount);
    }


    private void fetchKnowledgeInfo(List<SdkSearchResponse.SdkRetrieveItem<BaseItem>> pageItems) {
        // 过滤出需要获取知识信息的项（非外部知识库项且非知识库匹配项）
        List<SdkSearchResponse.SdkRetrieveItem<BaseItem>> normalItems = pageItems.stream()
                .filter(item -> !isExternalKnowledgeBaseItem(item) && !isKnowledgeBaseMatchItem(item))
                .collect(Collectors.toList());

        if (normalItems.isEmpty()) {
            return;
        }

        List<String> knowledgeCodes = normalItems.stream()
                .map(item -> item.getSource().getKnowledgeCode())
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());

        if (knowledgeCodes.isEmpty()) {
            return;
        }

        List<KnowledgeDTO> knowledgeDTOS = knowledgeRepository.findByCodes(knowledgeCodes);
        ImmutableMap<String, KnowledgeDTO> knowledgeMap = Maps.uniqueIndex(knowledgeDTOS, KnowledgeDTO::getCode);

        for (SdkSearchResponse.SdkRetrieveItem<BaseItem> item : normalItems) {
            String knowledgeCode = item.getSource().getKnowledgeCode();
            if (StringUtils.isNotBlank(knowledgeCode)) {
                KnowledgeDTO knowledgeDTO = knowledgeMap.get(knowledgeCode);
                if (knowledgeDTO != null) {
                    item.getSource().setCoverFileKey(BaseFileUrlUtils.getBaseDownloadUrl(knowledgeDTO.getCoverFileKey()));
                    item.getSource().setDescription(knowledgeDTO.getDescription());
                }
            }
        }
    }

    private boolean isExternalKnowledgeBaseItem(SdkSearchResponse.SdkRetrieveItem<BaseItem> item) {
        if (item == null || item.getSource() == null) {
            return false;
        }

        BaseItem source = item.getSource();
        return KnowledgeBaseType.EXTERNAL.equals(source.getKnowledgeBaseType());
    }

    private List<SdkSearchResponse.SdkRetrieveItem<BaseItem>> getKbMatchItems(SearchParam searchParam, Map<String, KnowledgeBaseDTO> searchKnowledgeBaseMap) {
        // 获取查询词进行知识库匹配
        String query = searchParam.getQuery();
        List<SdkSearchResponse.SdkRetrieveItem<BaseItem>> kbMatchItems = new ArrayList<>();

        // 只在普通搜索场景下添加知识库匹配结果
        SceneType sceneType = searchParam.getSceneType();
        boolean isSearchScene = SceneType.search.equals(sceneType);

        if (isSearchScene && StringUtils.isNotBlank(query)) {
            searchKnowledgeBaseMap.forEach((id, dto) -> {
                if (dto.getName() != null && dto.getName().contains(query)) {
                    kbMatchItems.add(buildKbMatchItem(id, dto));
                }
            });

            // 按创建时间倒排序（最新创建的在前面）
            kbMatchItems.sort((o1, o2) -> {
                Long createTime1 = o1.getSource().getCreateTime();
                Long createTime2 = o2.getSource().getCreateTime();
                if (createTime1 == null && createTime2 == null) {
                    return 0;
                }
                if (createTime1 == null) {
                    return 1;
                }
                if (createTime2 == null) {
                    return -1;
                }
                return Long.compare(createTime2, createTime1);
            });
        }
        return kbMatchItems;
    }

    private SdkSearchResponse.SdkRetrieveItem<BaseItem> buildKbMatchItem(String id, KnowledgeBaseDTO dto) {
        BaseItem baseItem = new BaseItem();
        baseItem.setKnowledgeBaseCode(id);
        baseItem.setKnowledgeBaseName(dto.getName());
        baseItem.setKnowledgeBaseType(dto.getType());
        baseItem.setKnowledgeType(KnowledgeType.KB);
        baseItem.setTitle(dto.getName());
        baseItem.setDescription(dto.getDesc());
        baseItem.setCreateName(dto.getCreateName());
        baseItem.setCreateTime(dto.getCreateTime().toEpochSecond(ZoneOffset.UTC) * 1000);
        baseItem.setUpdateTime(dto.getUpdateTime().toEpochSecond(ZoneOffset.UTC) * 1000);

        SdkSearchResponse.SdkRetrieveItem<BaseItem> item = new SdkSearchResponse.SdkRetrieveItem<>();
        item.setSource(baseItem);
        item.setScore(1.0f);
        return item;
    }

}
