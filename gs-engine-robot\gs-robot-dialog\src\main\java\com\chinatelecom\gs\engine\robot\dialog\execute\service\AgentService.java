package com.chinatelecom.gs.engine.robot.dialog.execute.service;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.chinatelecom.gs.engine.common.context.LogContextUtil;
import com.chinatelecom.gs.engine.common.utils.JsonUtils;
import com.chinatelecom.gs.engine.core.corekit.common.core.AnswerService;
import com.chinatelecom.gs.engine.robot.dialog.execute.dto.AgentActionRequest;
import com.chinatelecom.gs.engine.robot.dialog.execute.service.action.AgentActionService;
import com.chinatelecom.gs.engine.robot.dialog.execute.service.config.AgentConfigLoader;
import com.chinatelecom.gs.engine.robot.dialog.execute.service.noanswer.ToManuService;
import com.chinatelecom.gs.engine.robot.dialog.execute.service.utils.recommend.AgentRecomdQuesService;
import com.chinatelecom.gs.engine.robot.dialog.execute.service.utils.secure.AgentSafeFenceCheck;
import com.chinatelecom.gs.engine.robot.dialog.execute.service.utils.secure.AgentSecureCheck;
import com.chinatelecom.gs.engine.robot.dialog.memory.AgentMemoryService;
import com.chinatelecom.gs.engine.robot.dialog.memory.DialogMemoryService;
import com.chinatelecom.gs.engine.robot.sdk.enums.AnswerTypeEnum;
import com.chinatelecom.gs.engine.robot.sdk.enums.InteractionTypeEnum;
import com.chinatelecom.gs.engine.robot.sdk.enums.SystemIntentEnum;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.agent.AgentRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.agent.AgentResponse;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.agent.config.AgentConfig;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.dialog.DmToolIntent;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.dialog.enums.ChatTypeEnum;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.dialog.enums.DialogEngineType;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.SseMessageResponse;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.*;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.request.AnswerBuildRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.enums.DialogMessageTypeEnum;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.enums.SendMessageTypeEnum;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import javax.websocket.Session;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class AgentService {

    @Resource
    private AgentConfigLoader agentConfigLoader;

    @Resource
    private AgentActionService agentActionService;

    @Resource
    private AgentSecureCheck agentSecureCheck;

    @Resource
    private AgentRecomdQuesService agentRecomdQuesService;

    @Resource
    private AnswerService answerService;

    @Resource
    @Qualifier("apiChatExecutorPool")
    private ExecutorService apiChatExecutorPool;


    @Resource
    private AgentMemoryService agentMemoryService;

    @Resource
    private ToManuService toManuService;

    @Resource
    private AgentSafeFenceCheck agentSafeFenceCheck;

    @Resource
    private DialogMemoryService dialogMemoryService;

    public AgentResponse execute(AgentRequest agentRequest) {
        this.agentMemoryService.processRequest(agentRequest);

        AgentConfig agentConfig = this.agentConfigLoader.load(agentRequest.getAgentCode(),
                agentRequest.getHeader().getClient().getTest());
        agentRequest.getHeader().getClient().setAppCode(agentConfig.getTenantId());

        AgentResponse agentResponse = this.agentSecureCheck.preCheck(agentRequest, agentConfig);
        if (Objects.isNull(agentResponse)) {
            agentResponse = this.agentSafeFenceCheck.preCheck(agentRequest, agentConfig); //同步通过安全中心安全检测
        }
        if (Objects.nonNull(agentResponse)) {
            AgentResponse toManuResponse = this.toManuService.noAnswerCheck(agentRequest, agentConfig, agentResponse);
            if (!Objects.isNull(toManuResponse)) {
                agentResponse = toManuResponse;
            }
            this.agentMemoryService.processResponse(agentRequest, agentResponse);
            return agentResponse;
        }

        //推荐问题改成异步
        CountDownLatch countDownLatch = new CountDownLatch(1);
        List<String> recommendList = Lists.newArrayList();
        apiChatExecutorPool.submit(() -> {
            try {
                if (agentRequest.getChatType().equalsIgnoreCase(ChatTypeEnum.CHAT.getCode())) {
                    List<String> list = this.agentRecomdQuesService.queryRecommendList(agentRequest, agentConfig);
                    if (!CollectionUtils.isEmpty(list)) {
                        recommendList.addAll(list);
                    }
                }
            } catch (Exception e) {
                log.error("推荐问题发生异常！", e);
            } finally {
                countDownLatch.countDown();
            }
        });


        this.agentMemoryService.processRequest(agentRequest);

        agentResponse = this.executeByPlan(agentConfig, agentRequest);

        if (Objects.isNull(agentResponse) || CollectionUtils.isEmpty(agentResponse.getIntentAnswers())) {
            if (agentRequest.getChatType().equalsIgnoreCase(ChatTypeEnum.CONFIG.getCode())) {
                agentResponse = buildNotSupported(agentConfig.getAgentCode());
            } else {
                agentResponse = buildAgentDefaultResponse(agentConfig);
            }
            sendCoverNoAnswer(agentRequest, agentConfig);
        }

        AgentResponse toManuResponse = this.toManuService.noAnswerCheck(agentRequest, agentConfig, agentResponse);
        if (!Objects.isNull(toManuResponse)) {
            agentResponse = toManuResponse;
        }

        if (agentRequest.getChatType().equalsIgnoreCase(ChatTypeEnum.CHAT.getCode())) {
            if (!CollectionUtils.isEmpty(agentResponse.getIntentAnswers()) &&
                    agentResponse.getIntentAnswers().stream().noneMatch(intentAnswer -> intentAnswer.getDmToolIntent().getToolIntentId().equalsIgnoreCase(SystemIntentEnum.SENSITIVE.getCode()))) {
                try {
                    boolean await = countDownLatch.await(5L, TimeUnit.SECONDS);
                    if (await) {
                        agentResponse.setRecommendQuestions(recommendList);
                    } else {
                        log.error("等待推荐问题线程超时，未设置推荐问题列表");
                    }
                } catch (InterruptedException e) {
                    log.error("等待推荐问题线程发生异常！", e);
                    Thread.currentThread().interrupt();
                }
            }
        }

        LogContextUtil.addLog(agentRequest.getAgentCode(), "Agent执行【" + agentRequest.getOriginContent() + "】最终结果",
                agentResponse);


        this.agentMemoryService.processResponse(agentRequest, agentResponse);

        return agentResponse;

    }

    private void sendCoverNoAnswer(AgentRequest agentRequest, AgentConfig agentConfig) {
        AgentAnswer agentAnswer = buildNoAnswer(agentRequest.getHeader().getTrack().getDownMessageId(), agentConfig);
        SseMessageResponse noAnswerMessage = buildSseMessage(agentRequest, agentAnswer);
        try {
            if (InteractionTypeEnum.SSE.getCode()
                    .equals(agentRequest.getHeader().getClient().getInteractionType().getCode())) {
                SseEmitter sseEmitter = agentRequest.getHeader().getClient().getEmitter();
                synchronized (sseEmitter) {
                    sseEmitter.send(noAnswerMessage, MediaType.APPLICATION_JSON);
                }
            } else if (InteractionTypeEnum.WEBSOCKET.getCode()
                    .equals(agentRequest.getHeader().getClient().getInteractionType().getCode())) {
                Session session = agentRequest.getHeader().getClient().getSession();
                synchronized (session) {
                    if (session.isOpen()) {
                        session.getBasicRemote().sendText(JsonUtils.toJsonString(noAnswerMessage));
                    }
                }
            }
        } catch (Exception e) {
            log.error("【Agent】【Secure】 发送敏感词检测消息{} 失败 ", JSON.toJSONString(agentAnswer), e);
        }
    }

    @NotNull
    private SseMessageResponse buildSseMessage(AgentRequest agentRequest, AgentAnswer noAnswer) {
        Answer answer = BeanUtil.toBean(noAnswer, Answer.class);
        BotAnswer botAnswer = BeanUtil.toBean(answer, BotAnswer.class);
        botAnswer.setIntent(toAnswerIntent(noAnswer));
        SseMessageResponse secureMessageResponse = new SseMessageResponse();
        secureMessageResponse.setAnswer(botAnswer);
        secureMessageResponse.setUserId(agentRequest.getHeader().getUser().getUserId());
        secureMessageResponse.setUpMsgId(agentRequest.getHeader().getTrack().getMessageId());
        secureMessageResponse.setSessionId(agentRequest.getHeader().getTrack().getSessionId());
        secureMessageResponse.setDownMsgId(agentRequest.getHeader().getTrack().getDownMessageId());
        secureMessageResponse.setMessageType(DialogMessageTypeEnum.PART);
        secureMessageResponse.setEventType(SendMessageTypeEnum.COVER);
        return secureMessageResponse;
    }

    private AnswerIntent toAnswerIntent(AgentAnswer agentAnswer) {
        DmToolIntent dmToolIntent = agentAnswer.getDmToolIntent();
        AnswerIntent intent = new AnswerIntent();
        intent.setToolIntentId(dmToolIntent.getToolIntentId());
        intent.setDialogEngineType(dmToolIntent.getDialogEngineType());
        intent.setToolIntentName(dmToolIntent.getToolIntentName());
        intent.setSimilarContent(dmToolIntent.getSimilarContent());
        intent.setAnswerSourceType(agentAnswer.getAnswerSourceType());
        return intent;
    }

    private AgentAnswer buildNoAnswer(String messageId, AgentConfig agentConfig) {
        AnswerBuildRequest answerBuildRequest = new AnswerBuildRequest();
        answerBuildRequest.setAnswerTypeEnum(AnswerTypeEnum.SIMPLE_RICH_TEXT);
        answerBuildRequest.setContent(agentConfig.getDialogConfig().getNoAnswerScript());
        Answer answer = this.answerService.buildAnswer(answerBuildRequest);
        if (!ObjectUtils.isEmpty(messageId)) {
            answer.setMessageId(messageId);
        }

        AgentAnswer agentAnswer = BeanUtil.toBean(answer, AgentAnswer.class);

        DmToolIntent answerIntent = new DmToolIntent();
        answerIntent.setToolIntentId(SystemIntentEnum.NO_ANSWER.getCode());
        answerIntent.setToolIntentName(SystemIntentEnum.NO_ANSWER.getDesc());
        answerIntent.setDialogEngineType(DialogEngineType.SYSTEM);
        agentAnswer.setAnswerSourceType(AnswerSourceType.SYSTEM);
        agentAnswer.setDmToolIntent(answerIntent);

        return agentAnswer;
    }

    private AgentResponse executeByPlan(AgentConfig agentConfig, AgentRequest agentRequest) {
        AgentActionRequest actionRequest = BeanUtil.toBean(agentRequest, AgentActionRequest.class);
        actionRequest.setMessageCallback(agentRequest.getMessageCallback());
        actionRequest.setActionCallback(agentRequest.getActionCallback());
        actionRequest.setAgentConfig(agentConfig);
        actionRequest.setProcessQuery(agentRequest.getOriginContent());

        AgentResponse agentResponse = this.agentActionService.action(actionRequest);
        log.info("【Agent】最终生成结果 {}", agentResponse);

        LogContextUtil.addLog(actionRequest.getAgentCode(), "执行Action【" + actionRequest.getProcessQuery() + "】",
                agentResponse);

        AgentResponse secureResponse = this.agentSecureCheck.postCheck(agentRequest, agentConfig, agentResponse);
        if (Objects.isNull((secureResponse)) || (Objects.nonNull(secureResponse.getIntentAnswers()) && !SystemIntentEnum.SENSITIVE.getCode().equalsIgnoreCase(secureResponse.getIntentAnswers().get(0).getDmToolIntent().getToolIntentId()))) {
            secureResponse = this.agentSafeFenceCheck.postCheck(agentRequest, agentConfig, agentResponse); //同步通过安全中心安全检测
        }
        if (Objects.nonNull(secureResponse)) {
            agentResponse = secureResponse;
        }

        return agentResponse;
    }

    private AgentResponse buildNotSupported(String agentCode) {
        AgentAnswer agentAnswer = new AgentAnswer();

        DmToolIntent answerIntent = new DmToolIntent();
        answerIntent.setToolIntentId(SystemIntentEnum.NOT_SUPPORTED.getCode());//全局兜底意图编码
        answerIntent.setToolIntentName(SystemIntentEnum.NOT_SUPPORTED.getDesc());
        answerIntent.setDialogEngineType(DialogEngineType.SYSTEM);

        agentAnswer.setDmToolIntent(answerIntent);

        return new AgentResponse(Lists.newArrayList(agentAnswer));
    }

    private AgentResponse buildAgentDefaultResponse(AgentConfig agentConfig) {
        AnswerBuildRequest answerBuildRequest = new AnswerBuildRequest();
        answerBuildRequest.setAnswerTypeEnum(AnswerTypeEnum.SIMPLE_RICH_TEXT);
        answerBuildRequest.setContent(agentConfig.getDialogConfig().getNoAnswerScript());

        Answer answer = this.answerService.buildAnswer(answerBuildRequest);
        AgentAnswer agentAnswer = BeanUtil.toBean(answer, AgentAnswer.class);

        DmToolIntent answerIntent = new DmToolIntent();
        answerIntent.setToolIntentId(SystemIntentEnum.NO_ANSWER.getCode());
        answerIntent.setToolIntentName(SystemIntentEnum.NO_ANSWER.getDesc());
        answerIntent.setDialogEngineType(DialogEngineType.SYSTEM);
        agentAnswer.setAnswerSourceType(AnswerSourceType.CONFIG);
        agentAnswer.setDmToolIntent(answerIntent);
        return new AgentResponse(Lists.newArrayList(agentAnswer));
    }
}