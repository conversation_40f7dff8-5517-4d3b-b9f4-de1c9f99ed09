<?xml version="1.0" encoding="UTF-8"?>
<flow>

    <!--    文档解析主流程  -->
    <chain name="process_doc">
        THEN(DocPreprocess, DocConverter.retry(2), DocSplit.retry(2), DocPostprocess);
    </chain>

    <!--    问答文档解析主流程 -->
    <chain name="process_faq">
        THEN(DocPreprocess, FaqExtractor.retry(2), DocPostprocess);
    </chain>

    <!--    文档解析额外扩展流程 -->
    <chain name="process_extra">
        THEN(DocSummary);
    </chain>

    <!--    搜索主流程  -->
    <chain name="flow_search">
        THEN(QueryFilter, KnowledgeBaseFilter, SearchRecall, SearchRerank, SearchSort, SearchCollect, SearchPage);
    </chain>

    <!--    分片搜索主流程  -->
        <chain name="flow_search_chunk">
            THEN(
            QueryFilter,
            KnowledgeBaseFilter,
            WHEN(
            SearchRecall,
            ExternalSearch.maxWaitSeconds(10)
            ),
            SearchRerank,
            SearchSort,
            SearchChunkCollect,
            IntermediateResultProcessor,
            SearchPage
            );
        </chain>


    <!--    知识点过滤主流程  -->
    <chain name="flow_filter_knowledge">
        THEN(KnowledgeBaseFilter,FilterRecall, FilterChunkRecall, SearchCollect, SearchPage);
    </chain>

    <chain name="flow_search_suggest">
        THEN(KnowledgeBaseFilter,SearchSuggestRecall,SearchSuggestCollect);
    </chain>

</flow>
